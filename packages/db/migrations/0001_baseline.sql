-- Ledgerly BE - Baseline Migration
-- Creates foundational tables with RLS, audit, and double-entry constraints

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;

-- Core tenant/organization tables
CREATE TABLE tenants (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Tenant membership for multi-user orgs
CREATE TABLE tenant_memberships (
  id BIGSERIAL PRIMARY KEY,
  tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member')),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(tenant_id, user_id)
);

-- Sub-tenant entities (accounting files/companies)
CREATE TABLE entities (
  id BIGSERIAL PRIMARY KEY,
  tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  currency CHAR(3) NOT NULL DEFAULT 'EUR',
  fiscal_year_start DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(tenant_id, name)
);

-- Entity-level access control
CREATE TABLE entity_memberships (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'accountant', 'bookkeeper', 'viewer')),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(entity_id, user_id)
);

-- Operating modes per entity
CREATE TABLE operating_modes (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  mode TEXT NOT NULL CHECK (mode IN ('ledger', 'assist')),
  config JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(entity_id)
);

-- Chart of accounts
CREATE TABLE accounts (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  code TEXT NOT NULL,
  name TEXT NOT NULL,
  account_type TEXT NOT NULL CHECK (account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
  normal_balance TEXT NOT NULL CHECK (normal_balance IN ('debit', 'credit')),
  parent_id BIGINT REFERENCES accounts(id),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(entity_id, code)
);

-- VAT codes (Belgian tax system)
CREATE TABLE vat_codes (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT REFERENCES entities(id) ON DELETE CASCADE, -- NULL for system defaults
  code TEXT NOT NULL,
  name TEXT NOT NULL,
  rate DECIMAL(5,4) NOT NULL, -- e.g., 0.2100 for 21%
  is_purchase BOOLEAN DEFAULT FALSE,
  is_sale BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  CONSTRAINT vat_codes_entity_code_unique UNIQUE (entity_id, code)
);

-- VAT reporting periods
CREATE TABLE vat_periods (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'closed', 'filed')),
  filed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(entity_id, period_start, period_end)
);

-- Journal entries (transactions)
CREATE TABLE journals (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  journal_type TEXT NOT NULL DEFAULT 'general',
  reference TEXT,
  description TEXT NOT NULL,
  transaction_date DATE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  is_balanced BOOLEAN DEFAULT FALSE,
  UNIQUE(entity_id, journal_type, reference)
);

-- Journal lines (append-only ledger)
CREATE TABLE journal_lines (
  id BIGSERIAL PRIMARY KEY,
  journal_id BIGINT NOT NULL REFERENCES journals(id) ON DELETE CASCADE,
  account_id BIGINT NOT NULL REFERENCES accounts(id),
  description TEXT,
  debit_amount DECIMAL(15,2) CHECK (debit_amount >= 0),
  credit_amount DECIMAL(15,2) CHECK (credit_amount >= 0),
  vat_code_id BIGINT REFERENCES vat_codes(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  CHECK ((debit_amount IS NOT NULL AND credit_amount IS NULL) OR 
         (debit_amount IS NULL AND credit_amount IS NOT NULL))
);

-- Invoices
CREATE TABLE invoices (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  kind TEXT NOT NULL CHECK (kind IN ('sale', 'purchase')),
  number TEXT NOT NULL,
  counterparty_name TEXT NOT NULL,
  counterparty_vat TEXT,
  invoice_date DATE NOT NULL,
  due_date DATE,
  total_amount DECIMAL(15,2) NOT NULL,
  vat_amount DECIMAL(15,2) DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
  journal_id BIGINT REFERENCES journals(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(entity_id, kind, number)
);

-- Document storage references
CREATE TABLE documents (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL,
  filename TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type TEXT NOT NULL,
  invoice_id BIGINT REFERENCES invoices(id),
  journal_id BIGINT REFERENCES journals(id),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Bank accounts
CREATE TABLE bank_accounts (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  account_id BIGINT NOT NULL REFERENCES accounts(id),
  bank_name TEXT NOT NULL,
  account_number TEXT NOT NULL,
  iban TEXT,
  swift TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(entity_id, account_number)
);

-- Bank transactions (imported)
CREATE TABLE bank_transactions (
  id BIGSERIAL PRIMARY KEY,
  bank_account_id BIGINT NOT NULL REFERENCES bank_accounts(id) ON DELETE CASCADE,
  transaction_date DATE NOT NULL,
  value_date DATE NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  counterparty_name TEXT,
  counterparty_account TEXT,
  description TEXT NOT NULL,
  reference TEXT,
  transaction_id TEXT, -- Bank's unique ID
  is_reconciled BOOLEAN DEFAULT FALSE,
  journal_line_id BIGINT REFERENCES journal_lines(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(bank_account_id, transaction_id)
);

-- Reconciliation records
CREATE TABLE reconciliations (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  bank_account_id BIGINT NOT NULL REFERENCES bank_accounts(id),
  reconciliation_date DATE NOT NULL,
  statement_balance DECIMAL(15,2) NOT NULL,
  book_balance DECIMAL(15,2) NOT NULL,
  difference DECIMAL(15,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'closed')),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Domain events (outbox pattern)
CREATE TABLE domain_events (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT REFERENCES entities(id),
  event_type TEXT NOT NULL,
  aggregate_id BIGINT NOT NULL,
  aggregate_type TEXT NOT NULL,
  event_data JSONB NOT NULL,
  processed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Audit events
CREATE TABLE audit_events (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT REFERENCES entities(id),
  table_name TEXT NOT NULL,
  record_id BIGINT NOT NULL,
  operation TEXT NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
  old_values JSONB,
  new_values JSONB,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Indexes for performance
CREATE INDEX idx_tenant_memberships_user ON tenant_memberships(user_id);
CREATE INDEX idx_tenant_memberships_tenant ON tenant_memberships(tenant_id);
CREATE INDEX idx_entity_memberships_user ON entity_memberships(user_id);
CREATE INDEX idx_entity_memberships_entity ON entity_memberships(entity_id);
CREATE INDEX idx_accounts_entity ON accounts(entity_id);
CREATE INDEX idx_accounts_parent ON accounts(parent_id);
CREATE INDEX idx_journals_entity_date ON journals(entity_id, transaction_date);
CREATE INDEX idx_journal_lines_journal ON journal_lines(journal_id);
CREATE INDEX idx_journal_lines_account ON journal_lines(account_id);
CREATE INDEX idx_invoices_entity_kind ON invoices(entity_id, kind);
CREATE INDEX idx_bank_transactions_account ON bank_transactions(bank_account_id);
CREATE INDEX idx_bank_transactions_date ON bank_transactions(transaction_date);
CREATE INDEX idx_domain_events_processed ON domain_events(processed_at) WHERE processed_at IS NULL;
CREATE INDEX idx_audit_events_entity_table ON audit_events(entity_id, table_name);

-- RLS Setup: Enable RLS on all transactional tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE entity_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE operating_modes ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE vat_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE vat_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE journals ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE bank_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE bank_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE reconciliations ENABLE ROW LEVEL SECURITY;
ALTER TABLE domain_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_events ENABLE ROW LEVEL SECURITY;

-- Helper view for user entity access
CREATE VIEW v_user_entities AS
SELECT DISTINCT
  em.user_id,
  em.entity_id,
  e.tenant_id,
  em.role,
  e.name as entity_name,
  t.name as tenant_name
FROM entity_memberships em
JOIN entities e ON em.entity_id = e.id
JOIN tenants t ON e.tenant_id = t.id
WHERE em.user_id = auth.uid();

-- RLS Policies
-- Tenants: Users can only see tenants they belong to
CREATE POLICY tenant_access ON tenants FOR ALL TO authenticated
USING (id IN (SELECT tenant_id FROM tenant_memberships WHERE user_id = auth.uid()));

-- Tenant memberships: Users can see their own memberships
CREATE POLICY tenant_membership_access ON tenant_memberships FOR ALL TO authenticated
USING (user_id = auth.uid() OR tenant_id IN (
  SELECT tenant_id FROM tenant_memberships 
  WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
));

-- Entities: Users can see entities they have access to
CREATE POLICY entity_access ON entities FOR ALL TO authenticated
USING (id IN (SELECT entity_id FROM v_user_entities));

-- Entity memberships: Users can see memberships for entities they have access to
CREATE POLICY entity_membership_access ON entity_memberships FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Operating modes: Scoped by entity access
CREATE POLICY operating_mode_access ON operating_modes FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Accounts: Scoped by entity access
CREATE POLICY account_access ON accounts FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- VAT codes: Global defaults (entity_id IS NULL) or entity-specific
CREATE POLICY vat_code_access ON vat_codes FOR SELECT TO authenticated
USING (entity_id IS NULL OR entity_id IN (SELECT entity_id FROM v_user_entities));

CREATE POLICY vat_code_modify ON vat_codes FOR INSERT TO authenticated
WITH CHECK (entity_id IN (SELECT entity_id FROM v_user_entities WHERE role IN ('owner', 'admin', 'accountant')));

CREATE POLICY vat_code_update ON vat_codes FOR UPDATE TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities WHERE role IN ('owner', 'admin', 'accountant')));

-- VAT periods: Scoped by entity access
CREATE POLICY vat_period_access ON vat_periods FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Journals: Scoped by entity access
CREATE POLICY journal_access ON journals FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Journal lines: Access through parent journal's entity
CREATE POLICY journal_line_access ON journal_lines FOR SELECT TO authenticated
USING (journal_id IN (
  SELECT j.id FROM journals j 
  WHERE j.entity_id IN (SELECT entity_id FROM v_user_entities)
));

CREATE POLICY journal_line_insert ON journal_lines FOR INSERT TO authenticated
WITH CHECK (journal_id IN (
  SELECT j.id FROM journals j 
  WHERE j.entity_id IN (SELECT entity_id FROM v_user_entities WHERE role IN ('owner', 'admin', 'accountant', 'bookkeeper'))
));

-- Invoices: Scoped by entity access
CREATE POLICY invoice_access ON invoices FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Documents: Scoped by entity access
CREATE POLICY document_access ON documents FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Bank accounts: Scoped by entity access
CREATE POLICY bank_account_access ON bank_accounts FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Bank transactions: Access through parent bank account's entity
CREATE POLICY bank_transaction_access ON bank_transactions FOR ALL TO authenticated
USING (bank_account_id IN (
  SELECT ba.id FROM bank_accounts ba 
  WHERE ba.entity_id IN (SELECT entity_id FROM v_user_entities)
));

-- Reconciliations: Scoped by entity access
CREATE POLICY reconciliation_access ON reconciliations FOR ALL TO authenticated
USING (entity_id IN (SELECT entity_id FROM v_user_entities));

-- Domain events: Scoped by entity access (or global if entity_id IS NULL)
CREATE POLICY domain_event_access ON domain_events FOR ALL TO authenticated
USING (entity_id IS NULL OR entity_id IN (SELECT entity_id FROM v_user_entities));

-- Audit events: Scoped by entity access (or global if entity_id IS NULL)
CREATE POLICY audit_event_access ON audit_events FOR SELECT TO authenticated
USING (entity_id IS NULL OR entity_id IN (SELECT entity_id FROM v_user_entities));

-- Triggers for data integrity and audit

-- Prevent updates/deletes on journal_lines (append-only)
CREATE OR REPLACE FUNCTION prevent_journal_line_modification()
RETURNS TRIGGER AS $$
BEGIN
  RAISE EXCEPTION 'Journal lines are append-only. Modifications are not allowed.';
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_prevent_journal_line_update
  BEFORE UPDATE OR DELETE ON journal_lines
  FOR EACH ROW EXECUTE FUNCTION prevent_journal_line_modification();

-- Journal balance constraint (deferred)
CREATE OR REPLACE FUNCTION check_journal_balance()
RETURNS TRIGGER AS $$
DECLARE
  balance_check DECIMAL(15,2);
  journal_entity_id BIGINT;
BEGIN
  -- Get the journal's entity_id for the affected journal
  SELECT j.entity_id INTO journal_entity_id
  FROM journals j 
  WHERE j.id = COALESCE(NEW.journal_id, OLD.journal_id);
  
  -- Calculate balance for this journal
  SELECT COALESCE(SUM(COALESCE(jl.debit_amount, 0)) - SUM(COALESCE(jl.credit_amount, 0)), 0)
  INTO balance_check
  FROM journal_lines jl
  WHERE jl.journal_id = COALESCE(NEW.journal_id, OLD.journal_id);
  
  -- Check if balanced
  IF ABS(balance_check) > 0.001 THEN
    RAISE EXCEPTION 'Journal % is not balanced. Difference: %', 
      COALESCE(NEW.journal_id, OLD.journal_id), balance_check;
  END IF;
  
  -- Update journal balanced flag
  UPDATE journals 
  SET is_balanced = TRUE
  WHERE id = COALESCE(NEW.journal_id, OLD.journal_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Deferrable constraint trigger
CREATE CONSTRAINT TRIGGER trigger_journal_balance_check
  AFTER INSERT OR UPDATE OR DELETE ON journal_lines
  DEFERRABLE INITIALLY DEFERRED
  FOR EACH ROW EXECUTE FUNCTION check_journal_balance();

-- Updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_tenants_updated_at
  BEFORE UPDATE ON tenants
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_entities_updated_at
  BEFORE UPDATE ON entities
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_accounts_updated_at
  BEFORE UPDATE ON accounts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_invoices_updated_at
  BEFORE UPDATE ON invoices
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Seed Belgian VAT codes (system defaults)
INSERT INTO vat_codes (entity_id, code, name, rate, is_purchase, is_sale, is_active) VALUES
(NULL, 'BE21', 'Belgian VAT 21%', 0.2100, true, true, true),
(NULL, 'BE12', 'Belgian VAT 12%', 0.1200, true, true, true),
(NULL, 'BE06', 'Belgian VAT 6%', 0.0600, true, true, true),
(NULL, 'BE00', 'Belgian VAT 0% (exempt)', 0.0000, true, true, true),
(NULL, 'BEIC', 'Belgian Intra-Community', 0.0000, true, true, true),
(NULL, 'BEEX', 'Belgian Export', 0.0000, false, true, true);

-- Seed basic Belgian Chart of Accounts template
INSERT INTO vat_codes (entity_id, code, name, rate, is_purchase, is_sale, is_active) VALUES
(NULL, 'COA_TEMPLATE', 'Chart of Accounts Template', 0.0000, false, false, true);

-- Note: Specific COA accounts will be seeded per entity in dev_seed.sql