import { z } from 'zod'

// Journal Line contract
export const JournalLineSchema = z.object({
  journal_id: z.number(),
  account_id: z.number(),
  description: z.string().optional(),
  debit_amount: z.number().positive().optional(),
  credit_amount: z.number().positive().optional(),
  vat_code_id: z.number().optional()
}).refine(
  data => (data.debit_amount && !data.credit_amount) || (!data.debit_amount && data.credit_amount),
  {
    message: "Either debit_amount or credit_amount must be provided, but not both"
  }
)

export type JournalLine = z.infer<typeof JournalLineSchema>

// Post Journal contract
export const PostJournalSchema = z.object({
  entity_id: z.number(),
  journal_type: z.string().default('general'),
  reference: z.string().optional(),
  description: z.string().min(1),
  transaction_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  lines: z.array(JournalLineSchema).min(2)
}).refine(
  data => {
    const totalDebits = data.lines
      .filter(line => line.debit_amount)
      .reduce((sum, line) => sum + (line.debit_amount || 0), 0)
    
    const totalCredits = data.lines
      .filter(line => line.credit_amount)
      .reduce((sum, line) => sum + (line.credit_amount || 0), 0)
    
    return Math.abs(totalDebits - totalCredits) < 0.01
  },
  {
    message: "Journal must be balanced: sum of debits must equal sum of credits"
  }
)

export type PostJournal = z.infer<typeof PostJournalSchema>

// Invoice Create contract
export const InvoiceCreateSchema = z.object({
  entity_id: z.number(),
  kind: z.enum(['sale', 'purchase']),
  number: z.string().min(1),
  counterparty_name: z.string().min(1),
  counterparty_vat: z.string().optional(),
  invoice_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  total_amount: z.number().positive(),
  vat_amount: z.number().min(0).default(0),
  lines: z.array(z.object({
    description: z.string().min(1),
    quantity: z.number().positive(),
    unit_price: z.number().positive(),
    vat_code_id: z.number(),
    total: z.number().positive()
  })).min(1)
})

export type InvoiceCreate = z.infer<typeof InvoiceCreateSchema>

// Bank Import contract
export const BankImportSchema = z.object({
  entity_id: z.number(),
  bank_account_id: z.number(),
  format: z.enum(['csv', 'coda', 'mt940']),
  transactions: z.array(z.object({
    transaction_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    value_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    amount: z.number(),
    counterparty_name: z.string().optional(),
    counterparty_account: z.string().optional(),
    description: z.string().min(1),
    reference: z.string().optional(),
    transaction_id: z.string().optional()
  })).min(1)
})

export type BankImport = z.infer<typeof BankImportSchema>

// VAT Export contract
export const VatExportSchema = z.object({
  entity_id: z.number(),
  period_start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  period_end: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  format: z.enum(['xml', 'csv', 'xlsx']),
  include_zero_amounts: z.boolean().default(false)
})

export type VatExport = z.infer<typeof VatExportSchema>

// Operating Mode Switch contract
export const SwitchModeSchema = z.object({
  entity_id: z.number(),
  mode: z.enum(['ledger', 'assist']),
  config: z.record(z.unknown()).default({})
})

export type SwitchMode = z.infer<typeof SwitchModeSchema>

// Entity Role Grant contract
export const GrantEntityRoleSchema = z.object({
  entity_id: z.number(),
  user_id: z.string().uuid(),
  role: z.enum(['owner', 'admin', 'accountant', 'bookkeeper', 'viewer'])
})

export type GrantEntityRole = z.infer<typeof GrantEntityRoleSchema>

// Common response types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown().optional(),
  error: z.string().optional()
})

export type ApiResponse = z.infer<typeof ApiResponseSchema>

export const PaginationSchema = z.object({
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(20),
  total: z.number().min(0).optional(),
  pages: z.number().min(0).optional()
})

export type Pagination = z.infer<typeof PaginationSchema>