"""Pydantic models for request/response validation."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field, HttpUrl, validator


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    UNKNOWN = "unknown"


class HealthStatus(str, Enum):
    """Health status enumeration."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class ProcessingOptions(BaseModel):
    """Optional processing parameters."""
    
    ocr_language: str = Field(default="eng", description="OCR language code")
    extract_tables: bool = Field(default=False, description="Extract tables from PDF")
    extract_images: bool = Field(default=False, description="Extract images from PDF")
    ai_categorize: bool = Field(default=True, description="Perform AI categorization")
    ai_extract_entities: bool = Field(default=True, description="Extract entities with AI")
    confidence_threshold: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Minimum confidence threshold for AI results"
    )
    max_pages: Optional[int] = Field(
        default=None,
        ge=1,
        description="Maximum number of pages to process"
    )
    
    class Config:
        """Pydantic config."""
        extra = "allow"  # Allow additional fields


class DocumentProcessRequest(BaseModel):
    """Request model for document processing."""
    
    entity_id: str = Field(..., description="Unique identifier for the entity")
    file_url: HttpUrl = Field(..., description="URL to the file to process")
    options: Optional[ProcessingOptions] = Field(
        default=None,
        description="Optional processing parameters"
    )
    
    @validator('entity_id')
    def validate_entity_id(cls, v):
        """Validate entity ID format."""
        if not v or not v.strip():
            raise ValueError("Entity ID cannot be empty")
        if len(v.strip()) < 3:
            raise ValueError("Entity ID must be at least 3 characters long")
        return v.strip()
    
    class Config:
        """Pydantic config."""
        json_schema_extra = {
            "example": {
                "entity_id": "invoice-12345",
                "file_url": "https://example.com/document.pdf",
                "options": {
                    "ocr_language": "eng",
                    "extract_tables": True,
                    "ai_categorize": True,
                    "confidence_threshold": 0.8
                }
            }
        }


class DocumentProcessResponse(BaseModel):
    """Response model for document processing initiation."""
    
    task_id: str = Field(..., description="Unique task identifier")
    status: TaskStatus = Field(..., description="Initial task status")
    message: str = Field(..., description="Response message")
    estimated_completion_time: Optional[int] = Field(
        default=None,
        description="Estimated completion time in seconds"
    )
    
    class Config:
        """Pydantic config."""
        json_schema_extra = {
            "example": {
                "task_id": "abc123-def456-ghi789",
                "status": "processing",
                "message": "Document processing started successfully",
                "estimated_completion_time": 60
            }
        }


class AIAnalysisResult(BaseModel):
    """AI analysis result model."""
    
    document_type: Optional[str] = Field(default=None, description="Detected document type")
    confidence: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Confidence score")
    categories: List[str] = Field(default_factory=list, description="Document categories")
    entities: Dict[str, Any] = Field(default_factory=dict, description="Extracted entities")
    summary: Optional[str] = Field(default=None, description="Document summary")
    key_information: Dict[str, Any] = Field(
        default_factory=dict,
        description="Key information extracted"
    )
    language: Optional[str] = Field(default=None, description="Detected language")
    
    class Config:
        """Pydantic config."""
        json_schema_extra = {
            "example": {
                "document_type": "invoice",
                "confidence": 0.95,
                "categories": ["financial", "accounts_payable"],
                "entities": {
                    "vendor": "Acme Corp",
                    "amount": "1234.56",
                    "date": "2023-12-01",
                    "invoice_number": "INV-001"
                },
                "summary": "Invoice from Acme Corp for $1,234.56",
                "key_information": {
                    "total_amount": 1234.56,
                    "currency": "USD",
                    "due_date": "2023-12-31"
                },
                "language": "en"
            }
        }


class ProcessingMetadata(BaseModel):
    """Document processing metadata."""
    
    file_size: Optional[int] = Field(default=None, description="File size in bytes")
    file_type: Optional[str] = Field(default=None, description="File MIME type")
    page_count: Optional[int] = Field(default=None, description="Number of pages")
    processing_time: Optional[float] = Field(default=None, description="Processing time in seconds")
    ocr_confidence: Optional[float] = Field(default=None, description="OCR confidence score")
    extracted_images: Optional[int] = Field(default=None, description="Number of extracted images")
    extracted_tables: Optional[int] = Field(default=None, description="Number of extracted tables")
    character_count: Optional[int] = Field(default=None, description="Total character count")
    word_count: Optional[int] = Field(default=None, description="Total word count")
    
    class Config:
        """Pydantic config."""
        extra = "allow"


class TaskResult(BaseModel):
    """Complete task result model."""
    
    entity_id: str = Field(..., description="Entity identifier")
    file_url: str = Field(..., description="Original file URL")
    extracted_text: str = Field(..., description="Extracted text content")
    metadata: ProcessingMetadata = Field(..., description="Processing metadata")
    ai_analysis: AIAnalysisResult = Field(..., description="AI analysis results")
    processing_time: float = Field(..., description="Total processing time")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Processing timestamp")
    
    class Config:
        """Pydantic config."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TaskStatusResponse(BaseModel):
    """Response model for task status queries."""
    
    task_id: str = Field(..., description="Task identifier")
    status: TaskStatus = Field(..., description="Current task status")
    message: str = Field(..., description="Status message")
    progress: Optional[int] = Field(
        default=None,
        ge=0,
        le=100,
        description="Progress percentage (0-100)"
    )
    result: Optional[TaskResult] = Field(default=None, description="Task result if completed")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    created_at: Optional[datetime] = Field(default=None, description="Task creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")
    
    class Config:
        """Pydantic config."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        json_schema_extra = {
            "example": {
                "task_id": "abc123-def456-ghi789",
                "status": "processing",
                "message": "Document is being processed",
                "progress": 75,
                "result": None,
                "error": None,
                "created_at": "2023-12-01T10:00:00Z",
                "updated_at": "2023-12-01T10:02:30Z"
            }
        }


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: HealthStatus = Field(..., description="Overall health status")
    version: str = Field(..., description="Application version")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Health check timestamp")
    services: Dict[str, str] = Field(..., description="Service status details")
    uptime: Optional[float] = Field(default=None, description="Uptime in seconds")
    
    class Config:
        """Pydantic config."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "0.1.0",
                "timestamp": "2023-12-01T10:00:00Z",
                "services": {
                    "database": "connected",
                    "redis": "connected",
                    "celery": "running"
                },
                "uptime": 86400.0
            }
        }


class ErrorResponse(BaseModel):
    """Error response model."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    
    class Config:
        """Pydantic config."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# Database models (for ORM or raw SQL)
class DocumentProcessingResult(BaseModel):
    """Database model for processing results."""
    
    id: Optional[int] = Field(default=None, description="Primary key")
    entity_id: str = Field(..., description="Entity identifier")
    file_url: str = Field(..., description="File URL")
    task_id: str = Field(..., description="Celery task ID")
    status: TaskStatus = Field(..., description="Processing status")
    extracted_text: Optional[str] = Field(default=None, description="Extracted text")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Processing metadata")
    ai_analysis: Optional[Dict[str, Any]] = Field(default=None, description="AI analysis")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Update timestamp")
    
    class Config:
        """Pydantic config."""
        from_attributes = True  # For SQLAlchemy compatibility
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }