"""Main FastAPI application for the workers-py service."""

import logging
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse

from .config import settings
from .database import get_database, init_database
from .models import (
    DocumentProcessRequest,
    DocumentProcessResponse,
    TaskStatusResponse,
    HealthResponse,
)
from .tasks import process_document_task

# Configure logging
logging.basicConfig(level=getattr(logging, settings.LOG_LEVEL.upper()))
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    logger.info("Starting workers-py service")
    
    # Initialize database connection
    try:
        await init_database()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise
    
    yield
    
    logger.info("Shutting down workers-py service")


# Create FastAPI app
app = FastAPI(
    title="Workers-py",
    description="Python AI workers service for document processing",
    version="0.1.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Health check endpoint."""
    try:
        # Check database connection
        async with get_database() as db:
            await db.execute("SELECT 1")
        
        return HealthResponse(
            status="healthy",
            version="0.1.0",
            services={"database": "connected", "redis": "connected"}
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content=HealthResponse(
                status="unhealthy",
                version="0.1.0",
                services={"database": "disconnected", "redis": "unknown"}
            ).model_dump()
        )


@app.post("/process-document", response_model=DocumentProcessResponse)
async def process_document(
    request: DocumentProcessRequest,
    background_tasks: BackgroundTasks,
) -> DocumentProcessResponse:
    """
    Process a document asynchronously.
    
    Args:
        request: Document processing request containing entity_id and file URL
        background_tasks: FastAPI background tasks
        
    Returns:
        Response with task_id for tracking the processing status
    """
    try:
        logger.info(f"Processing document for entity {request.entity_id}")
        
        # Start the background task
        task = process_document_task.delay(
            entity_id=request.entity_id,
            file_url=request.file_url,
            options=request.options.model_dump() if request.options else {}
        )
        
        logger.info(f"Document processing task started: {task.id}")
        
        return DocumentProcessResponse(
            task_id=task.id,
            status="processing",
            message="Document processing started successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to start document processing: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start document processing: {str(e)}"
        )


@app.get("/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str) -> TaskStatusResponse:
    """
    Get the status of a processing task.
    
    Args:
        task_id: The ID of the task to check
        
    Returns:
        Task status information
    """
    try:
        from .tasks import celery_app
        
        task_result = celery_app.AsyncResult(task_id)
        
        if task_result.state == "PENDING":
            return TaskStatusResponse(
                task_id=task_id,
                status="pending",
                message="Task is waiting to be processed"
            )
        elif task_result.state == "PROGRESS":
            return TaskStatusResponse(
                task_id=task_id,
                status="processing",
                message="Task is currently being processed",
                progress=task_result.info.get("progress", 0) if task_result.info else 0
            )
        elif task_result.state == "SUCCESS":
            return TaskStatusResponse(
                task_id=task_id,
                status="completed",
                message="Task completed successfully",
                result=task_result.result,
                progress=100
            )
        elif task_result.state == "FAILURE":
            return TaskStatusResponse(
                task_id=task_id,
                status="failed",
                message=f"Task failed: {str(task_result.info)}",
                error=str(task_result.info)
            )
        else:
            return TaskStatusResponse(
                task_id=task_id,
                status="unknown",
                message=f"Unknown task state: {task_result.state}"
            )
            
    except Exception as e:
        logger.error(f"Failed to get task status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get task status: {str(e)}"
        )


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "workers_py.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )