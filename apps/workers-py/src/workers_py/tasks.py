"""Celery task definitions for async document processing."""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional
from urllib.parse import urlparse

import httpx
from celery import Celery, Task
from celery.signals import worker_ready

from .config import settings
from .database import get_database
from .services.ocr import OCRService
from .services.pdf import PDFService
from .services.ai import AIService

# Configure logging
logger = logging.getLogger(__name__)

# Create Celery app
celery_app = Celery(
    "workers-py",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
)

# Celery configuration
celery_app.conf.update(
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,
    timezone=settings.CELERY_TIMEZONE,
    enable_utc=True,
    task_track_started=True,
    task_time_limit=settings.TASK_TIMEOUT,
    task_soft_time_limit=settings.TASK_TIMEOUT - 30,
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=100,
)


class CallbackTask(Task):
    """Base task class with callback support."""
    
    def on_success(self, retval: Any, task_id: str, args: tuple, kwargs: dict) -> None:
        """Handle task success."""
        logger.info(f"Task {task_id} completed successfully")
    
    def on_failure(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo) -> None:
        """Handle task failure."""
        logger.error(f"Task {task_id} failed: {exc}")


@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Handle worker ready signal."""
    logger.info("Celery worker is ready")


def download_file(url: str, max_size_mb: int = None) -> Path:
    """
    Download a file from URL to temporary storage.
    
    Args:
        url: URL to download from
        max_size_mb: Maximum file size in MB
        
    Returns:
        Path to downloaded file
        
    Raises:
        ValueError: If file is too large or download fails
    """
    max_size = (max_size_mb or settings.MAX_FILE_SIZE_MB) * 1024 * 1024
    
    try:
        with httpx.stream("GET", url, timeout=30) as response:
            response.raise_for_status()
            
            # Check content length
            content_length = response.headers.get("content-length")
            if content_length and int(content_length) > max_size:
                raise ValueError(f"File too large: {content_length} bytes")
            
            # Determine file extension from URL or content-type
            parsed_url = urlparse(url)
            file_extension = Path(parsed_url.path).suffix
            
            if not file_extension:
                content_type = response.headers.get("content-type", "")
                if "pdf" in content_type:
                    file_extension = ".pdf"
                elif "image" in content_type:
                    file_extension = ".png"
                else:
                    file_extension = ".bin"
            
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(
                delete=False,
                suffix=file_extension
            )
            
            downloaded_size = 0
            for chunk in response.iter_bytes(chunk_size=8192):
                downloaded_size += len(chunk)
                if downloaded_size > max_size:
                    temp_file.close()
                    Path(temp_file.name).unlink(missing_ok=True)
                    raise ValueError(f"File too large: {downloaded_size} bytes")
                
                temp_file.write(chunk)
            
            temp_file.close()
            return Path(temp_file.name)
            
    except Exception as e:
        logger.error(f"Failed to download file from {url}: {e}")
        raise


@celery_app.task(bind=True, base=CallbackTask)
def process_document_task(
    self,
    entity_id: str,
    file_url: str,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Process a document asynchronously.
    
    Args:
        entity_id: Entity ID associated with the document
        file_url: URL to the file to process
        options: Optional processing options
        
    Returns:
        Processing results
    """
    options = options or {}
    result = {
        "entity_id": entity_id,
        "file_url": file_url,
        "status": "processing",
        "extracted_text": "",
        "metadata": {},
        "ai_analysis": {},
        "error": None
    }
    
    temp_file_path: Optional[Path] = None
    
    try:
        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={"progress": 10, "stage": "downloading"}
        )
        
        # Download the file
        logger.info(f"Downloading file from {file_url}")
        temp_file_path = download_file(file_url)
        
        # Determine file type
        file_extension = temp_file_path.suffix.lower()
        if file_extension not in ['.pdf', '.png', '.jpg', '.jpeg', '.tiff']:
            raise ValueError(f"Unsupported file type: {file_extension}")
        
        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={"progress": 30, "stage": "processing"}
        )
        
        # Initialize services
        ocr_service = OCRService()
        pdf_service = PDFService()
        ai_service = AIService()
        
        extracted_text = ""
        metadata = {}
        
        # Process based on file type
        if file_extension == '.pdf':
            logger.info("Processing PDF document")
            pdf_result = pdf_service.extract_text_and_metadata(temp_file_path)
            extracted_text = pdf_result.get("text", "")
            metadata = pdf_result.get("metadata", {})
            
            # If PDF text extraction failed, try OCR
            if not extracted_text.strip():
                logger.info("PDF text extraction failed, trying OCR")
                ocr_result = ocr_service.extract_text_from_pdf(temp_file_path)
                extracted_text = ocr_result.get("text", "")
                metadata.update(ocr_result.get("metadata", {}))
        
        else:
            # Image file - use OCR
            logger.info(f"Processing image file: {file_extension}")
            ocr_result = ocr_service.extract_text_from_image(temp_file_path)
            extracted_text = ocr_result.get("text", "")
            metadata = ocr_result.get("metadata", {})
        
        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={"progress": 70, "stage": "ai_analysis"}
        )
        
        # Perform AI analysis
        if extracted_text.strip():
            logger.info("Performing AI analysis")
            ai_analysis = ai_service.analyze_document(
                text=extracted_text,
                metadata=metadata,
                options=options
            )
        else:
            logger.warning("No text extracted, skipping AI analysis")
            ai_analysis = {"error": "No text extracted from document"}
        
        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={"progress": 90, "stage": "saving"}
        )
        
        # Save results to database
        result.update({
            "status": "completed",
            "extracted_text": extracted_text,
            "metadata": metadata,
            "ai_analysis": ai_analysis,
        })
        
        # Store in database
        async def save_to_database():
            async with get_database() as db:
                await db.execute(
                    """
                    INSERT INTO document_processing_results 
                    (entity_id, file_url, extracted_text, metadata, ai_analysis, status, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, NOW())
                    ON CONFLICT (entity_id, file_url) DO UPDATE SET
                        extracted_text = EXCLUDED.extracted_text,
                        metadata = EXCLUDED.metadata,
                        ai_analysis = EXCLUDED.ai_analysis,
                        status = EXCLUDED.status,
                        updated_at = NOW()
                    """,
                    entity_id,
                    file_url,
                    extracted_text,
                    metadata,
                    ai_analysis,
                    "completed"
                )
        
        # Note: In real implementation, you'd want to run this properly
        # For now, we'll just log it
        logger.info(f"Would save processing result to database for entity {entity_id}")
        
        logger.info(f"Document processing completed for entity {entity_id}")
        return result
        
    except Exception as e:
        logger.error(f"Document processing failed for entity {entity_id}: {e}")
        result.update({
            "status": "failed",
            "error": str(e)
        })
        
        # Update task state
        self.update_state(
            state="FAILURE",
            meta={"error": str(e), "entity_id": entity_id}
        )
        
        return result
        
    finally:
        # Clean up temporary file
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink(missing_ok=True)
            logger.info(f"Cleaned up temporary file: {temp_file_path}")


@celery_app.task
def health_check_task() -> Dict[str, str]:
    """Health check task for Celery workers."""
    return {
        "status": "healthy",
        "timestamp": str(logger.info),
        "worker": "celery"
    }


# Optional: Add periodic tasks
from celery.schedules import crontab

celery_app.conf.beat_schedule = {
    'cleanup-temp-files': {
        'task': 'workers_py.tasks.cleanup_temp_files',
        'schedule': crontab(minute=0, hour='*/6'),  # Every 6 hours
    },
}


@celery_app.task
def cleanup_temp_files() -> Dict[str, int]:
    """Clean up old temporary files."""
    temp_dir = Path(tempfile.gettempdir())
    cleaned_count = 0
    
    try:
        # Find and remove old temporary files
        for temp_file in temp_dir.glob("tmp*"):
            if temp_file.is_file() and temp_file.stat().st_mtime < (
                logger.info().timestamp() - 86400  # 24 hours
            ):
                temp_file.unlink(missing_ok=True)
                cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} temporary files")
        return {"cleaned_files": cleaned_count}
        
    except Exception as e:
        logger.error(f"Failed to cleanup temp files: {e}")
        return {"error": str(e), "cleaned_files": 0}