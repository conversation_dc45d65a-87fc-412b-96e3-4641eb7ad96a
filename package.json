{"name": "ledgerly-be", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"lint": "turbo run lint", "typecheck": "turbo run typecheck", "build": "turbo run build", "test": "turbo run test", "dev": "turbo run dev", "db:types": "supabase gen types typescript --linked > packages/types/src/supabase.ts", "db:migrate:local": "psql $DATABASE_URL -f packages/db/migrations/all.sql", "db:seed:local": "psql $DATABASE_URL -f packages/db/seeds/dev_seed.sql", "db:migrate:staging": "psql $STAGING_DATABASE_URL -f packages/db/migrations/all.sql", "db:types:staging": "supabase gen types typescript --db-url \"postgresql://postgres:<EMAIL>:5432/postgres\" > packages/types/src/supabase.ts", "db:migrate:prod": "psql $PROD_DATABASE_URL -f packages/db/migrations/all.sql", "db:types:prod": "supabase gen types typescript --project-ref ulhlnhwwxiolyskxulmm > packages/types/src/supabase.ts"}, "devDependencies": {"turbo": "^2.0.0"}}